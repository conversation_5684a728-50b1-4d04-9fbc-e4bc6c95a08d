/**
 * 文件相关IPC处理器
 */

const { dialog, shell } = require('electron');
const path = require('path');
const fs = require('fs').promises;
const { paths } = require('../../utils/paths');
const { fileService } = require('../../services/storage');
const resourceController = require('../../controllers/resource-controller');
const logger = require('../../utils/logger');
const { FILE } = require('./channels');
const { FileExtensions } = require('../../../shared/types/resource.js');

/**
 * 文件选择对话框配置
 */
const fileDialogConfig = {
  'video': {
    name: '视频文件',
    extensions: FileExtensions.VIDEO
  },
  'image': {
    name: '图片文件',
    extensions: FileExtensions.IMAGE
  },
  'app': {
    name: '应用文件',
    extensions: FileExtensions.APP
  },
  'other': {
    name: '所有文件',
    extensions: FileExtensions.OTHER
  }
};

/**
 * 文件相关IPC处理器
 */
const fileHandlers = {
  // 选择文件

  [FILE.SELECT]: async (_, options = {}) => {
    logger.debug('文件选择对话框参数', {
      fileType: options.fileType,
      properties: options.properties,
      availableKeys: Object.keys(fileDialogConfig),
      matchedFilter: fileDialogConfig[options.fileType] ? 'found' : 'not found'
    });

    // 构建对话框选项
    const dialogOptions = {
      properties: options.properties || ['openFile', 'multiSelections'],
      title: options.title || '选择文件'
    };

    // 如果不是选择文件夹模式，添加文件过滤器
    if (!options.properties || !options.properties.includes('openDirectory')) {
      const filter = fileDialogConfig[options.fileType] || fileDialogConfig['video'];
      dialogOptions.filters = [{
        name: filter.name,
        extensions: filter.extensions
      }];
    }

    logger.debug('文件选择对话框配置', {
      fileType: options.fileType,
      properties: dialogOptions.properties,
      filters: dialogOptions.filters
    });

    const result = await dialog.showOpenDialog(dialogOptions);

    if (result.canceled) {
      return [];
    }

    // 使用 Promise.all + fs.promises.stat 异步获取文件大小
    const filesWithSize = await Promise.all(
      result.filePaths.map(async (filePath) => {
        try {
          const stats = await fs.stat(filePath);
          return {
            path: filePath,
            name: path.basename(filePath),
            size: stats.size // 文件大小（字节）
          };
        } catch (error) {
          console.error(`获取文件大小失败: ${filePath}`, error);
          return {
            path: filePath,
            name: path.basename(filePath),
            size: 0, // 默认值（表示获取失败）
            error: '无法获取文件大小'
          };
        }
      })
    );

    return filesWithSize;
  },

  // 打开资源文件夹
  [FILE.OPEN_FOLDER]: async () => {
    await shell.openPath(paths.root);
    return true;
  },

  // 打开资源文件夹（新路径）
  [FILE.OPEN_RESOURCES_FOLDER]: async () => {
    // 打开 DeviceManagerResources 目录，而不是 config 子目录
    const resourcesRootDir = path.dirname(paths.resources.root);
    await shell.openPath(resourcesRootDir);
    return true;
  },

  // 检查配置文件是否存在
  [FILE.HAS_CONFIG]: async () => {
    return await resourceController.hasConfigFile();
  },

  // 获取资源文件路径
  [FILE.GET_PATH]: async (_, relativePath) => {
    // 确保相对路径不为空
    if (!relativePath) {
      logger.warn('获取资源文件路径失败: 相对路径为空');
      return null;
    }

    // 规范化路径，移除开头的斜杠
    const normalizedPath = relativePath.startsWith('/') ? relativePath.substring(1) : relativePath;

    // 构建完整路径
    const fullPath = path.join(paths.resources.root, normalizedPath);

    // 检查文件是否存在
    try {
      await fs.access(fullPath, fs.constants.F_OK);
    } catch (error) {
      logger.warn(`文件不存在: ${fullPath}`);
      return null;
    }

    logger.debug(`资源文件路径: ${fullPath}`);
    return `file:///${fullPath.replace(/\\/g, '/')}`;
  },

  // 获取文件统计信息
  [FILE.GET_STATS]: async (_, filePath) => {
    try {
      if (!filePath) {
        throw new Error('文件路径不能为空');
      }

      // 使用文件服务获取文件信息
      const fileInfo = await fileService.getFileInfo(filePath);

      logger.debug(`获取文件统计信息成功: ${filePath}`, {
        size: fileInfo.size,
        name: fileInfo.name
      });

      return {
        size: fileInfo.size,
        name: fileInfo.name,
        path: fileInfo.path,
        created: fileInfo.created,
        modified: fileInfo.modified,
        type: fileInfo.type,
        extension: fileInfo.extension
      };
    } catch (error) {
      logger.error(`获取文件统计信息失败: ${filePath}`, error);
      throw error;
    }
  },

  // 保存方案Logo
  [FILE.SAVE_SOLUTION_LOGO]: async (_, filePath) => {
    try {
      // 获取文件扩展名
      const ext = path.extname(filePath);

      // 构建目标文件名和路径，添加时间戳确保唯一性
      const timestamp = Date.now();
      const targetFileName = `dev_ctl_panel_logo_${timestamp}${ext}`;
      const targetPath = path.join(paths.resources.root, targetFileName);

      // 读取现有配置
      let config = await resourceController.readConfig(false, true);

      // 如果存在旧的logo文件，尝试删除
      if (config.logo) {
        const oldLogoPath = path.join(paths.resources.root, config.logo);
        try {
          // 检查文件是否存在
          await fs.access(oldLogoPath, fs.constants.F_OK);

          // 文件存在，尝试删除
          await fileService.deleteFile(oldLogoPath);
          logger.info(`删除旧方案Logo文件成功: ${oldLogoPath}`);
        } catch (fileErr) {
          // 文件不存在或无法访问，记录警告但继续执行
          logger.warn(`旧方案Logo文件不存在或无法访问: ${oldLogoPath}`, fileErr);
        }
      }

      // 确保资源目录存在
      await fileService._ensureDirectoryExists(paths.resources.root);

      // 复制文件
      const copied = await fileService.copyFile(filePath, targetPath);
      if (!copied) {
        logger.error('复制Logo文件失败');
        return null;
      }

      // 更新logo字段
      config.logo = targetFileName;

      // 保存配置文件
      const saved = await resourceController.saveConfig(config);
      if (!saved) {
        logger.error('保存配置失败');
        return null;
      }

      logger.info('保存方案Logo成功', { fileName: targetFileName });
      return targetFileName;
    } catch (error) {
      logger.error('保存方案Logo失败', error);
      return null;
    }
  },

  // 保存方案背景图
  [FILE.SAVE_SOLUTION_BACKGROUND]: async (_, filePath) => {
    try {
      // 获取文件扩展名
      const ext = path.extname(filePath);

      // 构建目标文件名和路径，添加时间戳确保唯一性
      const timestamp = Date.now();
      const targetFileName = `dev_ctl_panel_background_${timestamp}_panoramic${ext}`;
      const targetPath = path.join(paths.resources.root, targetFileName);

      // 读取现有配置
      let config = await resourceController.readConfig(false, true);

      // 如果存在旧的背景图文件，尝试删除
      if (config.background) {
        const oldBgPath = path.join(paths.resources.root, config.background);
        try {
          // 检查文件是否存在
          await fs.access(oldBgPath, fs.constants.F_OK);

          // 文件存在，尝试删除
          await fileService.deleteFile(oldBgPath);
          logger.info(`删除旧方案背景图文件成功: ${oldBgPath}`);
        } catch (fileErr) {
          // 文件不存在或无法访问，记录警告但继续执行
          logger.warn(`旧方案背景图文件不存在或无法访问: ${oldBgPath}`, fileErr);
        }
      }

      // 确保资源目录存在
      await fileService._ensureDirectoryExists(paths.resources.root);

      // 复制文件
      const copied = await fileService.copyFile(filePath, targetPath);
      if (!copied) {
        logger.error('复制背景图文件失败');
        return null;
      }

      // 更新background字段
      config.background = targetFileName;

      // 保存配置文件
      const saved = await resourceController.saveConfig(config);
      if (!saved) {
        logger.error('保存配置失败');
        return null;
      }

      logger.info('保存方案背景图成功', { fileName: targetFileName });
      return targetFileName;
    } catch (error) {
      logger.error('保存方案背景图失败', error);
      return null;
    }
  },

  // 删除方案Logo
  [FILE.DELETE_SOLUTION_LOGO]: async () => {
    try {
      // 读取配置文件
      let config = await resourceController.readConfig(false, true);
      if (!config) {
        logger.warn('删除Logo失败: 配置文件不存在');
        return false;
      }

      // 如果存在logo文件，删除它
      if (config.logo) {
        const logoPath = path.join(paths.resources.root, config.logo);

        try {
          // 检查文件是否存在
          await fs.access(logoPath, fs.constants.F_OK);

          // 文件存在，尝试删除
          await fileService.deleteFile(logoPath);
          logger.info(`删除方案Logo文件成功: ${logoPath}`);
        } catch (fileErr) {
          // 文件不存在或无法访问，记录警告但继续执行
          logger.warn(`方案Logo文件不存在或无法访问: ${logoPath}`, fileErr);
        }

        // 将logo字段设置为空字符串
        config.logo = "";

        // 保存更新后的配置
        const saved = await resourceController.saveConfig(config);
        if (!saved) {
          logger.error('保存配置失败');
          return false;
        }

        logger.info('删除方案Logo成功');
      } else {
        logger.info('没有方案Logo需要删除');
      }

      return true;
    } catch (err) {
      logger.error('删除方案Logo失败', err);
      // 返回 false 而不是抛出错误，这样不会中断应用
      return false;
    }
  },

  // 删除方案背景图
  [FILE.DELETE_SOLUTION_BACKGROUND]: async () => {
    try {
      // 读取配置文件
      let config = await resourceController.readConfig(false, true);
      if (!config) {
        logger.warn('删除背景图失败: 配置文件不存在');
        return false;
      }

      // 如果存在背景图文件，删除它
      if (config.background) {
        const bgPath = path.join(paths.resources.root, config.background);

        try {
          // 检查文件是否存在
          await fs.access(bgPath, fs.constants.F_OK);

          // 文件存在，尝试删除
          await fileService.deleteFile(bgPath);
          logger.info(`删除方案背景图文件成功: ${bgPath}`);
        } catch (fileErr) {
          // 文件不存在或无法访问，记录警告但继续执行
          logger.warn(`方案背景图文件不存在或无法访问: ${bgPath}`, fileErr);
        }

        // 将background字段设置为空字符串
        config.background = "";

        // 保存更新后的配置
        const saved = await resourceController.saveConfig(config);
        if (!saved) {
          logger.error('保存配置失败');
          return false;
        }

        logger.info('删除方案背景图成功');
      } else {
        logger.info('没有方案背景图需要删除');
      }

      return true;
    } catch (err) {
      logger.error('删除方案背景图失败', err);
      // 返回 false 而不是抛出错误，这样不会中断应用
      return false;
    }
  },

  // 保存应用Logo
  [FILE.SAVE_APP_LOGO]: async (_, filePath) => {
    // 获取文件扩展名
    const ext = path.extname(filePath);

    // 构建目标文件名和路径，使用时间戳
    const timestamp = Date.now();
    const targetFileName = `app_logo_${timestamp}${ext}`;
    const targetPath = path.join(paths.customData.root, targetFileName);

    // 读取现有配置
    let config = {};
    try {
      const configData = await fs.readFile(paths.customData.config, 'utf8');
      config = JSON.parse(configData);

      // 如果存在旧的logo文件，删除它
      if (config.appLogo) {
        const oldLogoPath = path.join(paths.customData.root, config.appLogo);
        await fileService.deleteFile(oldLogoPath).catch(err => {
          logger.warn(`删除旧应用Logo文件失败: ${oldLogoPath}`, err);
        });
      }
    } catch (err) {
      config = { appLogo: '' };
    }

    // 复制新文件
    const copied = await fileService.copyFile(filePath, targetPath);
    if (!copied) {
      throw new Error('复制应用Logo文件失败');
    }

    // 更新logo配置
    config.appLogo = targetFileName;

    // 保存配置
    await fs.writeFile(paths.customData.config, JSON.stringify(config, null, 2), 'utf8');

    // 发送事件通知应用更新 Logo
    if (_.sender) {
      _.sender.send('app-logo-updated');
    }

    logger.info('保存应用Logo成功', { fileName: targetFileName });
    return `file:///${targetPath.replace(/\\/g, '/')}`;
  },

  // 获取应用Logo
  [FILE.GET_APP_LOGO]: async () => {
    // 读取配置文件
    const configPath = paths.customData.config;
    let config = {};

    try {
      const configData = await fs.readFile(configPath, 'utf8');
      config = JSON.parse(configData);

      if (config.appLogo) {
        const logoPath = path.join(paths.customData.root, config.appLogo);
        return `file:///${logoPath.replace(/\\/g, '/')}`;
      }
    } catch (err) {
      logger.warn('读取配置文件失败', err);
    }

    // 返回默认logo路径
    return null;
  },

  // 删除应用Logo
  [FILE.DELETE_APP_LOGO]: async (event) => {
    const configPath = paths.customData.config;
    let config = {};

    try {
      // 确保配置目录存在
      await fs.mkdir(path.dirname(configPath), { recursive: true });

      // 尝试读取配置文件
      try {
        const configData = await fs.readFile(configPath, 'utf8');
        config = JSON.parse(configData);
      } catch (readErr) {
        // 如果文件不存在或解析失败，使用默认配置
        logger.warn('读取配置文件失败，使用默认配置', readErr);
        config = { appLogo: '' };
      }

      // 如果有 Logo，尝试删除
      if (config.appLogo) {
        const logoPath = path.join(paths.customData.root, config.appLogo);

        try {
          // 检查文件是否存在
          await fs.access(logoPath, fs.constants.F_OK);

          // 文件存在，尝试删除
          await fileService.deleteFile(logoPath);
          logger.info(`删除应用Logo文件成功: ${logoPath}`);
        } catch (fileErr) {
          // 文件不存在或无法访问，记录警告但继续执行
          logger.warn(`应用Logo文件不存在或无法访问: ${logoPath}`, fileErr);
        }

        // 无论文件删除是否成功，都更新配置
        config.appLogo = '';
        await fs.writeFile(configPath, JSON.stringify(config, null, 2), 'utf8');

        // 发送事件通知应用更新 Logo
        if (event && event.sender) {
          event.sender.send('app-logo-updated');
        }

        logger.info('删除应用Logo成功');
      } else {
        logger.info('没有应用Logo需要删除');

        // 即使没有 Logo 也发送更新事件，确保 UI 刷新
        if (event && event.sender) {
          event.sender.send('app-logo-updated');
        }
      }

      return true;
    } catch (err) {
      logger.error('删除应用Logo失败', err);
      // 返回 false 而不是抛出错误，这样不会中断应用
      return false;
    }
  }
};

module.exports = fileHandlers;
