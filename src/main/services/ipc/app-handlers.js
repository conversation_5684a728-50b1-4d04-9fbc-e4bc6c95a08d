/**
 * 应用相关IPC处理器
 */

const { app } = require('electron');
const path = require('path');
const fs = require('fs').promises;
const logger = require('../../utils/logger');
const { APP } = require('./channels');

/**
 * 获取应用版本信息
 * @returns {Promise<Object>} 版本信息对象
 */
const getAppVersion = async () => {
  try {
    // 从package.json获取版本信息
    const packageJsonPath = path.join(app.getAppPath(), 'package.json');
    const packageJsonContent = await fs.readFile(packageJsonPath, 'utf8');
    const packageJson = JSON.parse(packageJsonContent);

    // 获取公司信息
    const company = packageJson.company || packageJson.organization || '';

    // 确定产品名称 - 优先使用根级别的 productName
    const productName = packageJson.productName || packageJson.build?.productName || 'Device Manager';

    // 构建版本信息对象
    const versionInfo = {
      version: packageJson.version || '1.0.0',
      name: packageJson.name || 'PFDM',
      productName: productName,
      description: packageJson.description || '',
      copyright: packageJson.copyright || `© ${new Date().getFullYear()} ${company || ''}`,
      company: company
    };

    logger.debug('获取应用版本信息成功', versionInfo);
    return versionInfo;
  } catch (error) {
    logger.error('获取应用版本信息失败', error);
    // 尝试从 package.json 中获取基本信息
    let defaultProductName = 'Device Manager';
    let defaultName = 'PFDM';

    try {
      // 尝试读取 package.json 中的基本信息
      const packageJsonPath = path.join(app.getAppPath(), 'package.json');
      if (require('fs').existsSync(packageJsonPath)) {
        const packageJsonContent = require('fs').readFileSync(packageJsonPath, 'utf8');
        const packageJson = JSON.parse(packageJsonContent);
        defaultProductName = packageJson.productName || packageJson.build?.productName || 'Device Manager';
        defaultName = packageJson.name || 'PDM';
      }
    } catch (innerError) {
      logger.warn('读取 package.json 基本信息失败', innerError);
    }

    // 返回默认版本信息
    return {
      version: '1.0.0',
      name: defaultName,
      productName: defaultProductName,
      description: 'A tool for managing and controlling devices',
      copyright: `© ${new Date().getFullYear()}`,
      company: ''
    };
  }
};

/**
 * 同步获取版本号
 * 注意：此方法用于需要同步获取版本信息的场景，如初始化默认配置
 * @returns {string} 版本号
 */
const getVersionSync = () => {
  try {
    // 从package.json获取版本信息
    const packageJsonPath = path.join(app.getAppPath(), 'package.json');
    const packageJsonContent = require('fs').readFileSync(packageJsonPath, 'utf8');
    const packageJson = JSON.parse(packageJsonContent);
    return packageJson.version || '1.0.0';
  } catch (error) {
    logger.error('同步获取应用版本信息失败', error);
    return '1.0.0';
  }
};

/**
 * 获取平台信息
 * @returns {Promise<Object>} 平台信息对象
 */
const getPlatformInfo = async () => {
  try {
    const platformInfo = {
      platform: process.platform,
      isMac: process.platform === 'darwin',
      isWindows: process.platform === 'win32',
      isLinux: process.platform === 'linux',
      arch: process.arch,
      version: process.version
    };

    logger.debug('获取平台信息成功', platformInfo);
    return platformInfo;
  } catch (error) {
    logger.error('获取平台信息失败', error);
    // 返回默认平台信息
    return {
      platform: 'unknown',
      isMac: false,
      isWindows: false,
      isLinux: false,
      arch: 'unknown',
      version: 'unknown'
    };
  }
};

/**
 * 同步获取产品名称
 * 注意：此方法用于需要同步获取产品名称的场景
 * @returns {string} 产品名称
 */
const getProductNameSync = () => {
  try {
    // 从package.json获取产品名称
    const packageJsonPath = path.join(app.getAppPath(), 'package.json');
    const packageJsonContent = require('fs').readFileSync(packageJsonPath, 'utf8');
    const packageJson = JSON.parse(packageJsonContent);
    // 优先使用 build.productName，因为这是 electron-builder 使用的值
    return packageJson.build?.productName || packageJson.productName || 'Device Manager';
  } catch (error) {
    logger.error('同步获取产品名称失败', error);
    return 'Device Manager';
  }
};

/**
 * 应用相关IPC处理器
 */
const appHandlers = {
  // 获取应用版本信息
  [APP.GET_VERSION]: async () => {
    return await getAppVersion();
  },
  // 获取平台信息
  [APP.GET_PLATFORM_INFO]: async () => {
    return await getPlatformInfo();
  }
};

// 导出IPC处理器
module.exports = {
  APP,
  appHandlers,
  getAppVersion,
  getVersionSync,
  getProductNameSync,
  getPlatformInfo
};
