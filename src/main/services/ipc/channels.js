/**
 * IPC通道常量定义
 * 集中管理所有IPC通道名称
 */

/**
 * 设备相关IPC通道
 * @readonly
 * @enum {string}
 */
const DEVICE = {
  GET_LIST: 'get-device-list',
  DELETE: 'delete-device',
  SEND_COMMAND: 'send-command',
  GET_CONNECTED: 'get-connected-devices',
  GET_HISTORY: 'get-device-history',
  IS_ONLINE: 'is-device-online',
  ADD: 'add-device',
  GET_ALL: 'get-all-devices',
  IS_ADDED: 'is-device-added',
  PLAY_RESOURCE: 'play-resource',
  STOP_PLAY: 'stop-play',
  REBOOT: 'reboot-device',
  SHUTDOWN: 'shutdown-device',
  BATCH_PLAY: 'batch-play-resource',
  BATCH_STOP: 'batch-stop-play',
  // 音量控制相关
  GET_VOLUME: 'get-device-volume',
  SET_VOLUME: 'set-device-volume',
  // 屏幕控制相关
  SCREEN_ON: 'turn-screen-on',
  SCREEN_OFF: 'turn-screen-off',
  // 快捷入口
  QUICK_ACCESS: 'quick-access',
  // 定位控制相关
  LOCATE_START: 'start-locate-device',
  LOCATE_STOP: 'stop-locate-device',
  // 视野控制相关
  RESET_VIEW: 'reset-device-view',
  // 应用控制相关
  APP_CONTROL: 'control-app',
  // 设备组相关
  GET_GROUPS: 'get-device-groups',
  GET_GROUP: 'get-device-group',
  CREATE_GROUP: 'create-device-group',
  UPDATE_GROUP: 'update-device-group',
  DELETE_GROUP: 'delete-device-group',
  ADD_TO_GROUP: 'add-device-to-group',
  REMOVE_FROM_GROUP: 'remove-device-from-group',
  GET_DEVICES_BY_GROUP: 'get-devices-by-group',
  UPDATE_DEVICE_ID: 'update-device-id',
  // 批量控制相关
  BATCH_VOLUME: 'device:batch-volume',
  BATCH_SCREEN: 'device:batch-screen',
  BATCH_SHORTCUT: 'device:batch-shortcut'
};

/**
 * 文件相关IPC通道
 * @readonly
 * @enum {string}
 */
const FILE = {
  SELECT: 'select-files',
  OPEN_FOLDER: 'open-folder',
  OPEN_RESOURCES_FOLDER: 'open-resources-folder',
  HAS_CONFIG: 'has-config-file',
  GET_PATH: 'get-resource-file-path',
  SAVE_SOLUTION_LOGO: 'save-solution-logo',
  DELETE_SOLUTION_LOGO: 'delete-solution-logo',
  SAVE_SOLUTION_BACKGROUND: 'save-solution-background',
  DELETE_SOLUTION_BACKGROUND: 'delete-solution-background',
  SAVE_APP_LOGO: 'save-app-logo',
  GET_APP_LOGO: 'get-app-logo',
  DELETE_APP_LOGO: 'delete-app-logo'
};

/**
 * 设置相关IPC通道
 * @readonly
 * @enum {string}
 */
const SETTINGS = {
  SAVE: 'save-settings',
  GET: 'get-settings'
};

/**
 * 解决方案相关IPC通道
 * @readonly
 * @enum {string}
 */
const SOLUTION = {
  GET: 'get-solutions',
  CREATE: 'create-solution',
  DEPLOY: 'deploy-solution',
  EXPORT: 'export-solution',
  DELETE: 'delete-solution',
  UPDATE: 'update-solution',
  ADD_GROUP: 'add-solution-group',
  UPDATE_GROUP: 'update-solution-group',
  DELETE_GROUP: 'delete-solution-group',
  CANCEL_DEPLOY: 'cancel-deploy',
  CHECK_CONFIG_SIZE: 'solution:checkConfigSize'
};

/**
 * 资源相关IPC通道
 * @readonly
 * @enum {string}
 */
const RESOURCE = {
  CREATE: 'create-resource',
  GET: 'get-resources',
  DELETE: 'delete-resource',
  SAVE_CONFIG: 'save-config',
  DELETE_GROUP: 'delete-group',
  UPDATE: 'update-resource',
  PUBLISH: 'publish-resource',
  STOP: 'stop-resource'
};

/**
 * 窗口相关IPC通道
 * @readonly
 * @enum {string}
 */
const WINDOW = {
  CONTROL: 'window-control'
};

/**
 * 流媒体相关IPC通道
 * @readonly
 * @enum {string}
 */
const STREAM = {
  START: 'start-rtsp-stream',
  STOP: 'stop-rtsp-stream',
  GET_INFO: 'get-stream-info',
  GET_ALL: 'get-all-streams'
};

/**
 * 应用相关IPC通道
 * @readonly
 * @enum {string}
 */
const APP = {
  GET_VERSION: 'get-app-version',
  GET_PLATFORM_INFO: 'get-platform-info'
};

/**
 * 发布记录相关IPC通道
 * @readonly
 * @enum {string}
 */
const PUBLISH_RECORD = {
  GET_PUBLISH_RECORDS: 'get-publish-records',
  CREATE_PUBLISH_RECORD: 'create-publish-record',
  UPDATE_PUBLISH_RECORD: 'update-publish-record',
  STOP_PUBLISH_RECORD: 'stop-publish-record',
  ACTIVATE_PUBLISH_RECORD: 'activate-publish-record',
  DELETE_PUBLISH_RECORD: 'delete-publish-record',
  CLEAR_PUBLISH_RECORDS: 'clear-publish-records'
};

/**
 * 所有IPC通道
 * @readonly
 * @enum {Object}
 */
const CHANNELS = {
  DEVICE,
  FILE,
  SETTINGS,
  SOLUTION,
  RESOURCE,
  WINDOW,
  STREAM,
  APP,
  ...PUBLISH_RECORD
};

module.exports = { CHANNELS, DEVICE, FILE, SETTINGS, SOLUTION, RESOURCE, WINDOW, STREAM, APP, PUBLISH_RECORD };
