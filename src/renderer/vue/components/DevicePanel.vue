<template>
  <div class="device-panel">
    <div class="panel-header">
      <div class="panel-title-tabs" v-if="showModeToggle">
        <div
          class="title-tab"
          :class="{ active: !currentGroupMode }"
          @click="setMode(false)"
        >
          选择设备({{ modelValue.length ?? 0 }})
        </div>
        <!-- <h4
          class="title-tab"
          :class="{ active: currentGroupMode }"
          @click="setMode(true)"
        >
          分组
        </h4> -->
      </div>

      <h4 v-else>{{ title || "设备列表" }}</h4>
    </div>
    <div class="select-tip">
      视频A在设备1播放后，设备1不出现在视频B中，只有设备1先解除视频A，在可以在视频2的列表中看到设备1。
    </div>
    <div class="panel-content">
      <!-- 设备/分组列表 -->
      <div v-if="loading" class="loading-indicator">
        <div class="spinner"></div>
        <p>加载中...</p>
      </div>

      <!-- 分组模式 -->
      <div v-else-if="currentGroupMode" class="group-list">
        <!-- 分组过滤器 -->
        <div class="group-filter-container">
          <select
            v-model="selectedGroupId"
            class="group-filter"
            @change="onGroupFilterChange"
          >
            <option
              v-for="group in deviceGroups"
              :key="group.id"
              :value="group.id"
            >
              {{ group.name }} ({{ group.devices.length }})
            </option>
          </select>
        </div>

        <!-- 分组内容 -->
        <div v-if="deviceGroups.length === 0" class="empty-message">
          暂无设备分组
        </div>
        <div v-else-if="!selectedGroupId" class="empty-message">
          请选择一个设备分组
        </div>
        <div
          v-else-if="filteredGroupDevices.length === 0"
          class="empty-message"
        >
          该分组暂无设备
        </div>
        <div v-else class="device-list">
          <div class="device-list-header">
            <div class="header-checkbox">
              <el-checkbox
                :model-value="isAllGroupDevicesSelected"
                :indeterminate="isGroupDevicesIndeterminate"
                @change="toggleSelectAllGroupDevices"
              >
                全选
              </el-checkbox>
            </div>
            <div class="header-id">ID</div>
            <div class="header-sn">设备SN号</div>
            <div class="header-status">
              <div class="status-filter" @click.stop="toggleStatusFilter">
                <span>{{ getStatusFilterText(selectedStatus) }}</span>
                <i class="icon-arrow-down"></i>
                <div
                  v-show="showStatusFilter"
                  class="status-filter-menu"
                  @click.stop
                >
                  <div class="filter-item" @click="filterByStatus('all')">
                    全部
                  </div>
                  <div class="filter-item" @click="filterByStatus('offline')">
                    离线
                  </div>
                  <div
                    class="filter-item"
                    @click="filterByStatus('uncontrolled_playing')"
                  >
                    非受控播放
                  </div>
                  <div
                    class="filter-item"
                    @click="filterByStatus('uncontrolled_standby')"
                  >
                    非受控待机
                  </div>
                  <div
                    class="filter-item"
                    @click="filterByStatus('controlled_standby')"
                  >
                    受控待机
                  </div>
                  <div
                    class="filter-item"
                    @click="filterByStatus('controlled_playing')"
                  >
                    受控播放
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="device-items">
            <!-- 设备项 -->
            <div
              v-for="device in filteredGroupDevices"
              :key="device.sn"
              class="device-item"
              :class="{
                selected: modelValue.includes(device.sn),
                offline: !device.isOnline,
                disabled:
                  (!device.isOnline && disableOffline) ||
                  (disabledDevices && disabledDevices.includes(device.sn)),
                'in-active-publish':
                  disabledDevices && disabledDevices.includes(device.sn),
              }"
              @click="toggleDeviceSelection(device)"
            >
              <div class="item-checkbox">
                <el-checkbox
                  :model-value="isDeviceSelected(device)"
                  :disabled="
                    !device.isOnline ||
                    (disabledDevices && disabledDevices.includes(device.sn))
                  "
                  @change="(val) => toggleDeviceSelection(device)"
                />
              </div>
              <div class="item-id">
                {{
                  device.id ? device.id.toString().padStart(3, "0") : "未设置"
                }}
              </div>
              <div class="item-sn">{{ device.sn }}</div>
              <div class="item-status">
                <span>{{
                  !device.isOnline
                    ? "离线"
                    : device.status === "uncontrolled_playing"
                      ? "非受控播放"
                      : device.status === "uncontrolled_standby"
                        ? "非受控待机"
                        : device.status === "controlled_standby"
                          ? "受控待机"
                          : device.status === "controlled_playing"
                            ? "受控播放"
                            : "在线"
                }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 设备模式 -->
      <div v-else-if="devices.length === 0" class="empty-message">
        {{ emptyMessage || "暂无已添加的设备" }}
      </div>
      <div v-else class="device-list">
        <!-- 表头 -->
        <div class="device-list-header">
          <div class="header-checkbox">
            <el-checkbox
              :model-value="isAllDevicesSelected"
              :indeterminate="isDevicesIndeterminate"
              @change="toggleSelectAllDevices"
            >
              全选
            </el-checkbox>
          </div>
          <div class="header-id">ID</div>
          <div class="header-sn">设备SN号</div>
          <div class="header-status">
            <div class="status-filter" @click.stop="toggleStatusFilter">
              <span>{{ getStatusFilterText(selectedStatus) }}</span>
              <i class="icon-arrow-down"></i>
              <div
                v-show="showStatusFilter"
                class="status-filter-menu"
                @click.stop
              >
                <div class="filter-item" @click="filterByStatus('all')">
                  全部
                </div>
                <div class="filter-item" @click="filterByStatus('offline')">
                  离线
                </div>
                <div
                  class="filter-item"
                  @click="filterByStatus('uncontrolled_playing')"
                >
                  非受控播放
                </div>
                <div
                  class="filter-item"
                  @click="filterByStatus('uncontrolled_standby')"
                >
                  非受控待机
                </div>
                <div
                  class="filter-item"
                  @click="filterByStatus('controlled_standby')"
                >
                  受控待机
                </div>
                <div
                  class="filter-item"
                  @click="filterByStatus('controlled_playing')"
                >
                  受控播放
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 设备项列表 -->
        <div class="device-items">
          <div v-if="filteredDevices.length === 0" class="plhd-content">
            <img src="@assets/svg/control/empty_box.svg" alt="" />
            <div>暂无可用设备</div>
          </div>
          <!-- 设备项 -->
          <div
            v-else
            v-for="device in filteredDevices"
            :key="device.sn"
            class="device-item"
            :class="{
              selected: isDeviceSelected(device),
              offline: !device.isOnline,
              disabled:
                (!device.isOnline && disableOffline) ||
                (disabledDevices && disabledDevices.includes(device.sn)),
              'in-active-publish':
                disabledDevices && disabledDevices.includes(device.sn),
            }"
          >
            <div class="item-checkbox">
              <el-checkbox
                :model-value="isDeviceSelected(device)"
                :disabled="
                  !device.isOnline ||
                  (disabledDevices && disabledDevices.includes(device.sn))
                "
                @change="(val) => toggleDeviceSelection(device)"
              />
            </div>
            <div class="item-id">
              {{ device.id ? device.id.toString().padStart(3, "0") : "未设置" }}
            </div>
            <div class="item-sn">{{ device.sn }}</div>
            <div class="item-status">
              <span>{{
                !device.isOnline
                  ? "离线"
                  : device.status === "uncontrolled_playing"
                    ? "非受控播放"
                    : device.status === "uncontrolled_standby"
                      ? "非受控待机"
                      : device.status === "controlled_standby"
                        ? "受控待机"
                        : device.status === "controlled_playing"
                          ? "受控播放"
                          : "在线"
              }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  ref,
  computed,
  onMounted,
  onUnmounted,
  watch,
  nextTick,
  reactive,
} from "vue";
import { useElectronAPI } from "../plugins/electron";
import alertService from "../plugins/alert";
import ProgressBar from "./ProgressBar.vue";
import { ElCheckbox } from "element-plus";

const electronAPI = useElectronAPI();

// 定义组件属性
const props = defineProps({
  // 设备列表
  devices: {
    type: Array,
    default: () => [],
  },
  // 选中的设备SN列表（v-model）
  modelValue: {
    type: Array,
    default: () => [],
  },
  // 是否禁用离线设备
  disableOffline: {
    type: Boolean,
    default: true,
  },
  // 禁用的设备列表（例如已在活跃发布中的设备）
  disabledDevices: {
    type: Array,
    default: () => [],
  },
  // 面板标题
  title: {
    type: String,
    default: "设备列表",
  },
  // 空列表提示信息
  emptyMessage: {
    type: String,
    default: "暂无已添加的设备",
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false,
  },
  // 是否显示分组模式切换按钮
  showModeToggle: {
    type: Boolean,
    default: false,
  },
  // 是否处于分组模式
  isGroupMode: {
    type: Boolean,
    default: false,
  },
  // 是否显示刷新按钮
  showRefreshButton: {
    type: Boolean,
    default: true,
  },
  // 是否启用自动刷新
  autoRefresh: {
    type: Boolean,
    default: false,
  },
  // 自动刷新间隔（毫秒）
  refreshInterval: {
    type: Number,
    default: 1000,
  },
  // 设备分组列表
  deviceGroups: {
    type: Array,
    default: () => [],
  },
});

// 定义事件
const emit = defineEmits([
  "update:modelValue",
  "select",
  "deselect",
  "refresh",
  "update:isGroupMode",
]);

// 内部状态
const isGroupMode = ref(false);
const autoRefreshTimer = ref(null);

// 分组相关状态
const selectedGroupId = ref(null);

// 状态过滤相关
const showStatusFilter = ref(false);
const selectedStatus = ref("all");

// 设备状态枚举
const DeviceStatus = {
  OFFLINE: "offline", // 离线
  UNCONTROLLED_PLAYING: "uncontrolled_playing", // 非受控播放
  UNCONTROLLED_STANDBY: "uncontrolled_standby", // 非受控待机
  CONTROLLED_STANDBY: "controlled_standby", // 受控待机
  CONTROLLED_PLAYING: "controlled_playing", // 受控播放
};

// 更新设备状态
const updateDeviceStatus = (device) => {
  //  console.log('更新设备状态:', {
  //    sn: device.sn,
  //    isOnline: device.isOnline,
  //    controlled: device.controlled,
  //    playIndex: device.playIndex,
  //    currentStatus: device.status
  //  });

  if (!device.isOnline) {
    device.status = DeviceStatus.OFFLINE;
    console.log("设备离线:", device.sn);
    return;
  }

  if (device.controlled === 0) {
    // 非受控状态
    device.status =
      device.playIndex !== -1
        ? DeviceStatus.UNCONTROLLED_PLAYING
        : DeviceStatus.UNCONTROLLED_STANDBY;
    //console.log('非受控状态:', device.sn, device.status);
  } else if (device.controlled === 1) {
    // 受控状态
    device.status =
      device.playIndex !== -1
        ? DeviceStatus.CONTROLLED_PLAYING
        : DeviceStatus.CONTROLLED_STANDBY;
    //console.log('受控状态:', device.sn, device.status);
  }

  // console.log('设备状态更新完成:', {
  //   sn: device.sn,
  //   newStatus: device.status
  // });
};

// 监听设备数据变化，更新状态
watch(
  () => props.devices,
  (newDevices) => {
    console.log("设备列表发生变化，开始更新状态");
    newDevices.forEach((device) => {
      updateDeviceStatus(device);
    });
  },
  { deep: true, immediate: true }
);

// 检查设备是否被选中
const isDeviceSelected = (device) => {
  return props.modelValue.includes(device.sn);
};

// 计算属性 - 是否所有设备已选择
const isAllDevicesSelected = computed(() => {
  // 过滤出可选的设备（在线且不在禁用列表中）
  const selectableDevices = props.devices.filter(
    (device) =>
      device.isOnline &&
      (!props.disabledDevices || !props.disabledDevices.includes(device.sn))
  );

  // 如果没有可选设备，返回false
  if (selectableDevices.length === 0) return false;

  // 检查所有可选设备是否都被选中
  return selectableDevices.every((device) =>
    props.modelValue.includes(device.sn)
  );
});

// 计算属性 - 是否部分设备已选择
const isDevicesIndeterminate = computed(() => {
  // 过滤出可选的设备
  const selectableDevices = props.devices.filter(
    (device) =>
      device.isOnline &&
      (!props.disabledDevices || !props.disabledDevices.includes(device.sn))
  );

  // 如果没有可选设备，返回false
  if (selectableDevices.length === 0) return false;

  // 计算已选中的可选设备数量
  const selectedCount = selectableDevices.filter((device) =>
    props.modelValue.includes(device.sn)
  ).length;

  // 如果有选中但未全选，则为部分选中状态
  return selectedCount > 0 && selectedCount < selectableDevices.length;
});

// 计算属性 - 当前是否处于分组模式
const currentGroupMode = computed(() => {
  // 如果提供了isGroupMode属性，使用它
  if ("isGroupMode" in props) {
    return props.isGroupMode;
  }
  // 否则使用内部状态
  return isGroupMode.value;
});

// 计算属性 - 是否所有分组设备已选择
const isAllGroupDevicesSelected = computed(() => {
  if (!filteredGroupDevices.value.length) return false;

  const selectableDevices = props.disableOffline
    ? filteredGroupDevices.value.filter((device) => device.isOnline)
    : filteredGroupDevices.value;

  if (!selectableDevices.length) return false;

  return selectableDevices.every((device) =>
    props.modelValue.includes(device.sn)
  );
});

// 计算属性 - 分组设备是否部分选择
const isGroupDevicesIndeterminate = computed(() => {
  if (!filteredGroupDevices.value.length) return false;

  const selectableDevices = props.disableOffline
    ? filteredGroupDevices.value.filter((device) => device.isOnline)
    : filteredGroupDevices.value;

  if (!selectableDevices.length) return false;

  const selectedCount = selectableDevices.filter((device) =>
    props.modelValue.includes(device.sn)
  ).length;

  return selectedCount > 0 && selectedCount < selectableDevices.length;
});

// 方法 - 设备选择
const toggleDeviceSelection = (device) => {
  // 如果设备离线且禁用了离线设备选择，则不允许选中
  if (!device.isOnline && props.disableOffline) {
    return;
  }

  // 如果设备在禁用列表中，则不允许选中
  if (props.disabledDevices && props.disabledDevices.includes(device.sn)) {
    return;
  }

  const isSelected = isDeviceSelected(device);
  if (isSelected) {
    emit(
      "update:modelValue",
      props.modelValue.filter((sn) => sn !== device.sn)
    );
  } else {
    emit("update:modelValue", [...props.modelValue, device.sn]);
  }
};

// 方法 - 切换全选状态
const toggleSelectAllDevices = () => {
  // 过滤出可选的设备
  const selectableDevices = props.devices.filter(
    (device) =>
      device.isOnline &&
      (!props.disabledDevices || !props.disabledDevices.includes(device.sn))
  );

  if (isAllDevicesSelected.value) {
    // 如果当前是全选状态，则取消所有选择
    emit("update:modelValue", []);
  } else {
    // 如果当前不是全选状态，则选择所有可选设备的 SN
    emit(
      "update:modelValue",
      selectableDevices.map((device) => device.sn)
    );
  }
};

// 方法 - 切换分组/设备模式
const toggleGroupMode = () => {
  // 如果提供了isGroupMode属性，则通过事件更新它
  if ("isGroupMode" in props) {
    emit("update:isGroupMode", !props.isGroupMode);
  } else {
    // 否则使用内部状态
    isGroupMode.value = !isGroupMode.value;
  }
};

// 方法 - 设置特定模式
const setMode = (groupMode) => {
  // 如果提供了isGroupMode属性，则通过事件更新它
  if ("isGroupMode" in props) {
    emit("update:isGroupMode", groupMode);
  } else {
    // 否则使用内部状态
    isGroupMode.value = groupMode;
  }
};

// 方法 - 刷新设备列表
const refreshDevices = () => {
  console.log("刷新设备列表");
  emit("refresh");
  // 更新所有设备的状态
  props.devices.forEach((device) => {
    updateDeviceStatus(device);
  });
};

// 方法 - 处理分组过滤器变化
const onGroupFilterChange = () => {
  if (selectedGroupId.value) {
    // 如果选择了分组，过滤出分组内的设备
    const selectedGroup = props.deviceGroups.find(
      (group) => group.id === selectedGroupId.value
    );
    if (selectedGroup) {
      // 从设备列表中找出属于该分组的设备
      filteredGroupDevices.value = props.devices.filter((device) =>
        selectedGroup.devices.includes(device.sn)
      );

      // 自动全选该分组内的设备
      selectAllGroupDevices();
    } else {
      filteredGroupDevices.value = [];
    }
  } else {
    // 如果没有选择分组，清空过滤后的设备列表
    filteredGroupDevices.value = [];
  }
};

// 方法 - 全选分组内设备
const selectAllGroupDevices = () => {
  // 获取可选择的设备（如果禁用离线设备，则只选择在线设备）
  const selectableDevices = props.disableOffline
    ? filteredGroupDevices.value.filter((device) => device.isOnline)
    : filteredGroupDevices.value;

  if (!selectableDevices.length) return;

  // 创建新的选择列表，保留原有选择的设备，并添加当前分组的设备
  const currentSelection = [...props.modelValue];

  // 添加当前分组中尚未选择的设备
  selectableDevices.forEach((device) => {
    if (!currentSelection.includes(device.sn)) {
      currentSelection.push(device.sn);
    }
  });

  // 更新选择
  emit("update:modelValue", currentSelection);
};

// 方法 - 全选/取消全选分组内设备
const toggleSelectAllGroupDevices = (event) => {
  const isChecked = event.target.checked;

  if (isChecked) {
    // 全选分组内设备
    selectAllGroupDevices();
  } else {
    // 取消选择分组内所有设备
    const newSelection = props.modelValue.filter(
      (sn) => !filteredGroupDevices.value.some((device) => device.sn === sn)
    );

    // 更新选择
    emit("update:modelValue", newSelection);
  }
};

// 设置自动刷新
const setupAutoRefresh = () => {
  if (props.autoRefresh && !autoRefreshTimer.value) {
    console.log("设置自动刷新，间隔:", props.refreshInterval);
    autoRefreshTimer.value = setInterval(() => {
      console.log("执行自动刷新");
      refreshDevices();
    }, props.refreshInterval);
  }
};

// 清除自动刷新
const clearAutoRefresh = () => {
  if (autoRefreshTimer.value) {
    clearInterval(autoRefreshTimer.value);
    autoRefreshTimer.value = null;
  }
};

// 监听设备状态更新事件
const handleDeviceStatusUpdated = async ({ ip, status }) => {
  if (!status.sn) return;

  const device = props.devices.find((d) => d.sn === status.sn);
  if (!device) return;

  // 更新设备状态
  device.isOnline = status.deviceStatus === 1;
  // 兼容 controlled 和 controled 两种拼写
  device.controlled =
    status.controlled !== undefined
      ? status.controlled
      : status.controled !== undefined
        ? status.controled
        : 0;
  device.playIndex = status.playIndex !== undefined ? status.playIndex : -1;
  device.battery = status.battery || "-";
  device.onuse = status.onuse !== undefined ? status.onuse : 0;

  // 更新设备状态
  updateDeviceStatus(device);
};

// 设置事件监听器
const setupEventListeners = () => {
  // 监听设备状态更新事件
  electronAPI.onDeviceStatusUpdate(handleDeviceStatusUpdated);
  console.log("DevicePanel: 事件监听器设置完成");
};

// 移除事件监听器
const removeEventListeners = () => {
  // 移除设备状态更新事件监听器
  electronAPI.offDeviceStatusUpdate(handleDeviceStatusUpdated);
  console.log("DevicePanel: 事件监听器已移除");
};

// 生命周期钩子
onMounted(() => {
  // 设置事件监听器
  setupEventListeners();
  document.addEventListener("click", handleClickOutside);
});

onUnmounted(() => {
  // 移除事件监听器
  removeEventListeners();
  document.removeEventListener("click", handleClickOutside);
});

// 监听autoRefresh属性变化
watch(
  () => props.autoRefresh,
  (newValue) => {
    if (newValue) {
      setupAutoRefresh();
    } else {
      clearAutoRefresh();
    }
  }
);

// 监听设备列表变化，更新分组过滤
watch(
  () => props.devices,
  () => {
    if (currentGroupMode.value) {
      onGroupFilterChange();
    }
  },
  { deep: true }
);

// 监听分组列表变化，更新分组过滤
watch(
  () => props.deviceGroups,
  () => {
    if (currentGroupMode.value) {
      // 如果当前选中的分组不再存在，重置选择
      if (selectedGroupId.value) {
        const groupExists = props.deviceGroups.some(
          (group) => group.id === selectedGroupId.value
        );
        if (!groupExists && props.deviceGroups.length > 0) {
          // 如果有其他分组，选择第一个
          selectedGroupId.value = props.deviceGroups[0].id;
        } else if (!groupExists) {
          // 如果没有分组，清空选择
          selectedGroupId.value = null;
        }
      } else if (props.deviceGroups.length > 0 && !selectedGroupId.value) {
        // 如果有分组但没有选择，选择第一个
        selectedGroupId.value = props.deviceGroups[0].id;
      }
      onGroupFilterChange();
    }
  },
  { deep: true }
);

// 监听分组模式变化
watch(currentGroupMode, (newValue) => {
  if (newValue && props.deviceGroups.length > 0 && !selectedGroupId.value) {
    // 如果切换到分组模式且有分组但没有选择，选择第一个分组
    selectedGroupId.value = props.deviceGroups[0].id;
    onGroupFilterChange();
  }
});

// 初始化分组设备列表
onMounted(() => {
  // 如果处于分组模式且有分组，默认选中第一个分组
  if (currentGroupMode.value && props.deviceGroups.length > 0) {
    selectedGroupId.value = props.deviceGroups[0].id;
    onGroupFilterChange();
  }
});

// 监听单个设备属性变化
const watchDeviceProperties = (device) => {
  //console.log('设置设备属性监听:', device.sn);
  watch(
    () => [device.isOnline, device.controlled, device.playIndex],
    (newValues, oldValues) => {
      // console.log('设备属性变化:', {
      //   sn: device.sn,
      //   oldValues,
      //   newValues
      // });
      updateDeviceStatus(device);
    },
    { deep: true }
  );
};

// 在设备列表变化时设置监听器
watch(
  () => props.devices,
  (newDevices) => {
    newDevices.forEach((device) => {
      watchDeviceProperties(device);
    });
  },
  { deep: true }
);

// 切换状态过滤器显示
const toggleStatusFilter = (event) => {
  event.stopPropagation();
  console.log("Toggle status filter:", !showStatusFilter.value);
  showStatusFilter.value = !showStatusFilter.value;

  if (showStatusFilter.value) {
    // 获取点击元素的位置
    const rect = event.currentTarget.getBoundingClientRect();
    const menu = document.querySelector(".status-filter-menu");
    if (menu) {
      // 设置菜单位置
      menu.style.top = `${rect.bottom + window.scrollY}px`;
      menu.style.left = `${rect.left + window.scrollX}px`;
    }
  }
};

// 根据状态过滤
const filterByStatus = (status) => {
  console.log("Filter by status:", status);
  selectedStatus.value = status;
  showStatusFilter.value = false;
};

// 点击外部关闭过滤器
const handleClickOutside = (event) => {
  const filterElement = document.querySelector(".status-filter");
  if (filterElement && !filterElement.contains(event.target)) {
    console.log("Click outside, closing filter");
    showStatusFilter.value = false;
  }
};

// 过滤后的设备列表
const filteredDevices = computed(() => {
  if (selectedStatus.value === "all") {
    return props.devices;
  }
  return props.devices.filter((device) => {
    if (selectedStatus.value === "offline") {
      return !device.isOnline;
    }
    return device.status === selectedStatus.value;
  });
});

// 过滤后的分组设备列表
const filteredGroupDevices = computed(() => {
  // 首先根据分组过滤设备
  const groupDevices = props.devices.filter((device) => {
    const selectedGroup = props.deviceGroups.find(
      (group) => group.id === selectedGroupId.value
    );
    return selectedGroup && selectedGroup.devices.includes(device.sn);
  });

  // 然后根据状态过滤
  if (selectedStatus.value === "all") {
    return groupDevices;
  }

  return groupDevices.filter((device) => {
    if (selectedStatus.value === "offline") {
      return !device.isOnline;
    }
    return device.status === selectedStatus.value;
  });
});

// 在 script setup 部分添加 getStatusFilterText 方法
const getStatusFilterText = (status) => {
  switch (status) {
    case "all":
      return "全部";
    case "offline":
      return "离线";
    case "uncontrolled_playing":
      return "非受控播放";
    case "uncontrolled_standby":
      return "非受控待机";
    case "controlled_standby":
      return "受控待机";
    case "controlled_playing":
      return "受控播放";
    default:
      return "状态";
  }
};
</script>

<style scoped>
.device-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--color-background);
  min-height: 48px; /* 确保最小高度 */
  box-sizing: border-box;
  flex-wrap: nowrap; /* 防止换行 */
}

.panel-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--color-text-primary);
  white-space: nowrap; /* 防止文字换行 */
  overflow: hidden; /* 隐藏溢出内容 */
  text-overflow: ellipsis; /* 显示省略号 */
  flex-shrink: 1; /* 允许收缩 */
  min-width: 0; /* 允许收缩到0 */
}

/* 标题选项卡样式 */
.panel-title-tabs {
  display: flex;
  gap: 16px;
  flex-shrink: 1;
  min-width: 0;
}

.title-tab {
  cursor: pointer;
  padding: 4px 0;
  position: relative;
  color: var(--color-text-secondary);
  transition: all 0.2s ease;
  font-weight: 500;

  color: var(--color-menu-text);
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}

.title-tab:hover {
  color: var(--color-primary);
}

.title-tab.active {
  color: var(--color-primary);
  font-weight: 600;
}

/* .title-tab.active::after {
  content: "";
  position: absolute;
  bottom: -12px; 
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--color-primary);
  border-radius: 2px;
} */

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 25px;
  border-radius: 10px;
  background-color: var(--color-box-bg);
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: var(--spacing-md);
}

.spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: var(--spacing-sm);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.empty-message {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--color-text-secondary);
  font-size: 14px;
  padding: var(--spacing-md);
  text-align: center;
}

/* 分组过滤器样式 */
.group-filter-container {
  margin-bottom: var(--spacing-md);
}

.group-filter {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  background-color: var(--color-card-background);
  font-size: 14px;
  color: var(--color-text-primary);
  appearance: none;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 16px;
  cursor: pointer;
  transition: border-color 0.2s;
}

.group-filter:focus {
  outline: none;
  border-color: var(--color-primary);
}

/* 深色主题下的下拉箭头图标 */
[data-theme="dark"] .group-filter {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23ccc' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
}

.device-list {
  width: 100%;
  position: relative;
  max-height: 400px;
  overflow-y: auto;
}

.device-list-header {
  display: grid;
  grid-template-columns: 80px 60px 1fr 120px;
  gap: 10px;
  padding: 8px 10px;
  border-bottom: 1px solid var(--color-border);
  font-weight: 500;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 2;
}

.device-items {
  display: flex;
  flex-direction: column;
  position: relative;
  margin-top: 0;
}

.plhd-content {
  display: flex;
  flex-direction: column;
  margin: 18px auto 25px;
  gap: 30px;
  align-items: center;

  color: #595b6a;
  text-align: center;
  font-size: 16px;
  font-style: normal;
  font-weight: 480;
  line-height: normal;
}

.device-item {
  display: grid;
  grid-template-columns: 80px 60px 1fr 120px;
  gap: 10px;
  padding: 8px 10px;
  background-color: var(--color-card-background-online);
  border-bottom: 1px solid var(--color-border);
  cursor: pointer;
  transition: all 0.2s;
  align-items: center;
}

.device-item.offline {
  background-color: var(--color-background-light);
  opacity: 0.7;
}

.device-item:hover {
  background-color: var(--color-card-background);
}

.device-item.offline:hover {
  background-color: var(--color-background-light);
  opacity: 0.8;
}

.device-item.selected {
  background-color: var(--color-primary-light);
}

.device-item.selected:hover {
  background-color: var(--color-primary-light);
  opacity: 0.9;
}

.device-item.offline.selected {
  background-color: var(--color-background-light);
  opacity: 0.8;
}

.device-item.offline.selected:hover {
  background-color: var(--color-background-light);
  opacity: 0.9;
}

.device-item .item-checkbox {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 40px;
  padding-left: 10px;
}

.device-item .item-id {
  font-size: 13px;
  font-weight: 500;
  color: var(--color-text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-left: 10px;
  width: 60px;
}

.device-item .item-sn {
  font-size: 12px;
  color: var(--color-text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-left: 10px;
  width: 100%;
}

.device-item .item-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: var(--color-text-secondary);
  padding-left: 10px;
  width: 120px;
}

.header-checkbox {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 80px;
  padding-left: 10px;
}

.header-id,
.header-sn,
.header-status {
  font-size: 13px;
  font-weight: 500;
  color: var(--color-text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-left: 10px;
  width: 120px;
}

.header-id {
  width: 60px;
}

.header-sn {
  width: 100%;
}

.selected-count-banner {
  position: sticky;
  top: 0;
  background-color: var(--color-primary-bg);
  color: var(--color-primary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  margin-bottom: var(--spacing-xs);
  z-index: 1;
}

.device-item.disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.device-item.in-active-publish {
  position: relative;
}

.device-item.in-active-publish::after {
  content: "已发布";
  position: absolute;
  top: 5px;
  right: 5px;
  font-size: 10px;
  padding: 2px 4px;
  background-color: var(--color-warning, orange);
  color: white;
  border-radius: 3px;
}

.device-status-indicator {
  display: none;
}

.status-offline,
.status-uncontrolled-playing,
.status-uncontrolled-standby,
.status-controlled-standby,
.status-controlled-playing {
  display: none;
}

/* 按钮样式 */
.btn {
  padding: 4px 8px;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  font-size: 13px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.btn i {
  margin-right: 4px;
}

.btn-text {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  white-space: nowrap; /* 防止文字换行 */
  overflow: hidden; /* 隐藏溢出内容 */
  text-overflow: ellipsis; /* 显示省略号 */
  min-width: 0;
}

.btn-text:hover {
  color: var(--color-primary);
  background-color: rgba(0, 0, 0, 0.05);
}

/* 暗色主题下的文本按钮 */
[data-theme="dark"] .btn-text {
  color: var(--color-text-secondary);
}

[data-theme="dark"] .btn-text:hover {
  color: var(--color-primary-light);
  background-color: rgba(255, 255, 255, 0.1);
}

/* 暗色主题下的文本按钮中的图标 */
[data-theme="dark"] .btn-text .icon-refresh {
  filter: brightness(0) invert(1);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .device-item .item-id,
  .device-item .item-sn {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }
}

.select-tip {
  color: #6e6e6e;
  font-size: 10px;
  font-style: normal;
  font-weight: 340;
  line-height: normal;
  margin-bottom: 8px;
}

.select-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
  gap: 8px;
  white-space: nowrap;
}

.select-checkbox input[type="checkbox"] {
  display: none;
}

.checkbox-custom {
  position: relative;
  display: inline-block;
  width: 18px;
  height: 18px;
  background-color: var(--color-white);
  border: 2px solid var(--color-border);
  border-radius: 4px;
  transition: all 0.2s;
  flex-shrink: 0;
}

.checkbox-text {
  font-size: 14px;
  color: var(--color-text-secondary);
  white-space: nowrap;
}

.select-checkbox input[type="checkbox"]:checked ~ .checkbox-custom {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.select-checkbox input[type="checkbox"]:checked ~ .checkbox-custom:after {
  content: "";
  position: absolute;
  left: 5px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid var(--color-white);
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.select-checkbox input[type="checkbox"]:indeterminate ~ .checkbox-custom {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.select-checkbox input[type="checkbox"]:indeterminate ~ .checkbox-custom:after {
  content: "";
  position: absolute;
  left: 3px;
  top: 7px;
  width: 10px;
  height: 2px;
  background-color: var(--color-white);
}

/* 状态过滤器样式 */
.status-filter {
  position: relative;
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
  user-select: none;
}

.status-filter:hover {
  background-color: var(--color-background-light);
}

.icon-arrow-down {
  width: 8px;
  height: 8px;
  border: solid var(--color-text-secondary);
  border-width: 0 1px 1px 0;
  transform: rotate(45deg);
  margin-top: -2px;
  margin-left: 4px;
  opacity: 0.6;
}

.status-filter:hover .icon-arrow-down {
  opacity: 0.8;
}

.status-filter-menu {
  position: fixed;
  background-color: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 9999;
  min-width: 120px;
  margin-top: 4px;
}

.filter-item {
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
  white-space: nowrap;
  color: var(--color-text-primary);
}

.filter-item:hover {
  background-color: var(--color-background-light);
}

/* 添加新的样式类 */
.checkbox-custom.checked {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.checkbox-custom.checked:after {
  content: "";
  position: absolute;
  left: 5px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid var(--color-white);
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

/* 移除自定义复选框相关样式 */
.select-checkbox,
.checkbox-custom,
.checkbox-text {
  display: none;
}

/* 调整 Element Plus 复选框样式 */
:deep(.el-checkbox) {
  --el-checkbox-checked-bg-color: var(--color-primary);
  --el-checkbox-checked-border-color: var(--color-primary);
  --el-checkbox-disabled-checked-fill: var(--color-background-light);
}

:deep(.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner) {
  background-color: var(--color-background-light);
  border-color: var(--color-border);
}

/* 调整表头复选框样式 */
.header-checkbox {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 80px;
  padding-left: 10px;
}

:deep(.header-checkbox .el-checkbox) {
  --el-checkbox-checked-bg-color: var(--color-primary);
  --el-checkbox-checked-border-color: var(--color-primary);
  --el-checkbox-disabled-checked-fill: var(--color-background-light);
}

:deep(.header-checkbox .el-checkbox__label) {
  font-size: 14px;
  color: var(--color-text-secondary);
}

:deep(
  .header-checkbox
    .el-checkbox__input.is-disabled.is-checked
    .el-checkbox__inner
) {
  background-color: var(--color-background-light);
  border-color: var(--color-border);
}
</style>
