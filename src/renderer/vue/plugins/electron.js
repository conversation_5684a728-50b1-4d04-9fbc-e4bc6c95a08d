/**
 * Electron API插件
 * 封装Electron API，使其在Vue组件中易于使用
 */

// 创建一个全局可访问的Electron API对象
export const useElectronAPI = () => {
  // 确保window.electronAPI存在
  if (!window.electronAPI) {
    console.error('Electron API不可用');
    return {};
  }

  return {
    // 窗口控制
    minimizeWindow: () => window.electronAPI.windowControl('minimize'),
    maximizeWindow: () => window.electronAPI.windowControl('maximize'),
    closeWindow: () => window.electronAPI.windowControl('close'),

    // 设备相关
    getDeviceList: () => window.electronAPI.getDeviceList(),
    sendCommand: (sn, command) => window.electronAPI.sendCommand(sn, command),
    getConnectedDevices: () => window.electronAPI.getConnectedDevices(),
    getDeviceHistory: () => window.electronAPI.getDeviceHistory(),
    isDeviceOnline: (sn) => window.electronAPI.isDeviceOnline(sn),
    deleteDevice: (sn) => window.electronAPI.deleteDevice(sn),
    addDevice: (sn) => window.electronAPI.addDevice(sn),
    getAllDevices: () => window.electronAPI.getAllDevices(),
    isDeviceAdded: (sn) => window.electronAPI.isDeviceAdded(sn),
    updateDeviceId: (sn, id, forceSwap = false) => window.electronAPI.updateDeviceId(sn, id, forceSwap),
    getOnlineDevices: () => window.electronAPI.getConnectedDevices(), // 获取在线设备

    // 设备事件监听
    onDeviceConnected: (callback) => {
      if (window.electronAPI.onDeviceConnected) {
        window.electronAPI.onDeviceConnected(callback);
      } else {
        console.warn('onDeviceConnected API不可用');
      }
    },

    offDeviceConnected: (callback) => {
      if (window.electronAPI.offDeviceConnected) {
        window.electronAPI.offDeviceConnected(callback);
      } else {
        console.warn('offDeviceConnected API不可用');
      }
    },

    onDeviceDisconnected: (callback) => {
      if (window.electronAPI.onDeviceDisconnected) {
        window.electronAPI.onDeviceDisconnected(callback);
      } else {
        console.warn('onDeviceDisconnected API不可用');
      }
    },

    offDeviceDisconnected: (callback) => {
      if (window.electronAPI.offDeviceDisconnected) {
        window.electronAPI.offDeviceDisconnected(callback);
      } else {
        console.warn('offDeviceDisconnected API不可用');
      }
    },

    onDeviceStatusUpdate: (callback) => {
      if (window.electronAPI.onDeviceStatusUpdate) {
        window.electronAPI.onDeviceStatusUpdate(callback);
      } else {
        console.warn('onDeviceStatusUpdate API不可用');
      }
    },

    offDeviceStatusUpdate: (callback) => {
      if (window.electronAPI.offDeviceStatusUpdate) {
        window.electronAPI.offDeviceStatusUpdate(callback);
      } else {
        console.warn('offDeviceStatusUpdate API不可用');
      }
    },

    onDeviceAdded: (callback) => {
      if (window.electronAPI.onDeviceAdded) {
        window.electronAPI.onDeviceAdded(callback);
      } else {
        console.warn('onDeviceAdded API不可用');
      }
    },

    offDeviceAdded: (callback) => {
      if (window.electronAPI.offDeviceAdded) {
        window.electronAPI.offDeviceAdded(callback);
      } else {
        console.warn('offDeviceAdded API不可用');
      }
    },

    removeAllListeners: (eventName) => {
      if (window.electronAPI.removeAllListeners) {
        window.electronAPI.removeAllListeners(eventName);
      } else {
        console.warn('removeAllListeners API不可用');
      }
    },

    // 事件发送
    emit: (eventName, ...args) => {
      if (window.electronAPI.emit) {
        return window.electronAPI.emit(eventName, ...args);
      } else {
        console.warn(`emit API不可用，无法发送事件: ${eventName}`);
        return null;
      }
    },

    // 资源相关
    getResources: () => window.electronAPI.getResources(),
    getResourceList: () => window.electronAPI.getResources(), // 别名，保持与原代码兼容
    createResource: (data) => window.electronAPI.createResource(data),
    updateResource: (resource) => window.electronAPI.updateResource(resource),
    deleteResource: (uuid) => window.electronAPI.deleteResource(uuid),
    publishResource: (resourceIndex, deviceList, loopPlay) =>
      window.electronAPI.publishResource(resourceIndex, deviceList, loopPlay),
    stopResource: (deviceList, resourceType, packageName) => window.electronAPI.stopResource(deviceList, resourceType, packageName),

    // 方案相关
    getSolutions: () => window.electronAPI.getSolutions(),
    createSolution: (data) => window.electronAPI.createSolution(data),
    updateSolution: (data) => window.electronAPI.updateSolution(data),
    deploySolution: (data) => window.electronAPI.deploySolution(data),
    exportSolution: (data) => window.electronAPI.exportSolution(data),
    cancelDeploy: (deviceSN) => window.electronAPI.cancelDeploy(deviceSN),
    deleteSolution: (uuid) => window.electronAPI.deleteSolution(uuid),
    saveSolutionLogo: (filePath) => window.electronAPI.saveSolutionLogo(filePath),
    deleteSolutionLogo: () => window.electronAPI.deleteSolutionLogo(),
    saveSolutionBackground: (filePath) => window.electronAPI.saveSolutionBackground(filePath),
    deleteSolutionBackground: () => window.electronAPI.deleteSolutionBackground(),
    checkConfigFolderSize: () => window.electronAPI.checkConfigFolderSize(),

    // 方案分组相关
    addSolutionGroup: (solutionId, name, description) =>
      window.electronAPI.addSolutionGroup(solutionId, name, description),
    updateSolutionGroup: (solutionId, oldName, newName, description) =>
      window.electronAPI.updateSolutionGroup(solutionId, oldName, newName, description),
    deleteSolutionGroup: (solutionId, name) =>
      window.electronAPI.deleteSolutionGroup(solutionId, name),

    // 设置相关
    getSettings: () => window.electronAPI.getSettings(),
    saveSettings: (settings) => window.electronAPI.saveSettings(settings),
    saveBasicSettings: (settings) => window.electronAPI.saveBasicSettings(settings),
    saveAdvancedSettings: (settings) => window.electronAPI.saveAdvancedSettings(settings),

    // 文件相关
    selectFiles: (options) => window.electronAPI.selectFiles(options),
    openResourcesFolder: () => window.electronAPI.openResourcesFolder(),
    getResourceFilePath: (relativePath) => window.electronAPI.getResourceFilePath(relativePath),
    hasConfigFile: () => window.electronAPI.hasConfigFile(),
    getFileStats: (filePath) => window.electronAPI.getFileStats ? window.electronAPI.getFileStats(filePath) : Promise.reject(new Error('getFileStats API不可用')),

    // 应用Logo相关
    getAppLogo: () => window.electronAPI.getAppLogo(),
    saveAppLogo: (filePath) => window.electronAPI.saveAppLogo(filePath),
    deleteAppLogo: () => window.electronAPI.deleteAppLogo(),

    // 分组相关
    deleteGroup: (groupName) => window.electronAPI.deleteGroup(groupName),
    saveConfig: (config) => window.electronAPI.saveConfig(config),

    // 设备组相关
    getDeviceGroups: () => window.electronAPI.getDeviceGroups(),
    getDeviceGroup: (groupId) => window.electronAPI.getDeviceGroup(groupId),
    createDeviceGroup: (name, description) => window.electronAPI.createDeviceGroup(name, description),
    updateDeviceGroup: (groupId, data) => window.electronAPI.updateDeviceGroup(groupId, data),
    deleteDeviceGroup: (groupId) => window.electronAPI.deleteDeviceGroup(groupId),
    addDeviceToGroup: (groupId, sn) => window.electronAPI.addDeviceToGroup(groupId, sn),
    removeDeviceFromGroup: (groupId, sn) => window.electronAPI.removeDeviceFromGroup(groupId, sn),
    getDevicesByGroup: (groupId) => window.electronAPI.getDevicesByGroup(groupId),

    // RTSP流代理服务相关
    startRtspStream: (sn, rtspUrl) => window.electronAPI.startRtspStream(sn, rtspUrl),
    stopRtspStream: (sn) => window.electronAPI.stopRtspStream(sn),
    getStreamInfo: (sn) => window.electronAPI.getStreamInfo(sn),
    getAllStreams: () => window.electronAPI.getAllStreams(),

    // 应用信息相关
    getAppVersion: () => window.electronAPI.getAppVersion(),
    getVersion: () => window.electronAPI.getVersion(),
    getPlatformInfo: () => window.electronAPI.getPlatformInfo(),

    // 更新相关
    checkUpdate: () => window.electronAPI.checkUpdate(),
    downloadUpdate: () => window.electronAPI.downloadUpdate(),
    installUpdate: () => window.electronAPI.installUpdate(),
    onUpdateProgress: (callback) => {
      if (window.electronAPI.onUpdateProgress) {
        return window.electronAPI.onUpdateProgress(callback);
      } else {
        console.warn('onUpdateProgress API不可用');
      }
    },
    offUpdateProgress: (handler) => {
      if (window.electronAPI.offUpdateProgress) {
        window.electronAPI.offUpdateProgress(handler);
      } else {
        console.warn('offUpdateProgress API不可用');
      }
    },

    // 发布记录相关
    getPublishRecords: () => window.electronAPI.getPublishRecords(),
    createPublishRecord: (recordData) => window.electronAPI.createPublishRecord(recordData),
    updatePublishRecord: (id, updateData) => window.electronAPI.updatePublishRecord(id, updateData),
    stopPublishRecord: (id) => window.electronAPI.stopPublishRecord(id),
    activatePublishRecord: (id) => window.electronAPI.activatePublishRecord(id),
    deletePublishRecord: (id) => window.electronAPI.deletePublishRecord(id),
    clearPublishRecords: () => window.electronAPI.clearPublishRecords(),

    // 事件监听 - 使用新的API，支持移除事件监听
    onTransferProgress: (callback) => {
      if (window.electronAPI.onTransferProgress) {
        window.electronAPI.onTransferProgress(callback);
      } else {
        console.warn('onTransferProgress API不可用');
      }
    },

    offTransferProgress: (callback) => {
      if (window.electronAPI.offTransferProgress) {
        window.electronAPI.offTransferProgress(callback);
      } else {
        console.warn('offTransferProgress API不可用');
      }
    },

    onDeployError: (callback) => {
      if (window.electronAPI.onDeployError) {
        window.electronAPI.onDeployError(callback);
      } else {
        console.warn('onDeployError API不可用');
      }
    },

    offDeployError: (callback) => {
      if (window.electronAPI.offDeployError) {
        window.electronAPI.offDeployError(callback);
      } else {
        console.warn('offDeployError API不可用');
      }
    }
  };
};

// 创建Vue插件
export const electronPlugin = {
  install: (app) => {
    app.config.globalProperties.$electronAPI = useElectronAPI();

    // 提供一个全局的inject key，使组件可以通过inject获取electronAPI
    app.provide('electronAPI', useElectronAPI());
  }
};
