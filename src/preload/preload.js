const { contextBridge, ipc<PERSON>enderer } = require('electron');

// 检查是否为开发环境
const isDev = process.env.NODE_ENV === 'development';

// 日志函数，只在开发环境下输出
const devLog = (...args) => {
  if (isDev) {
    console.log(...args);
  }
};

// 警告函数，只在开发环境下输出详细信息
const devWarn = (message, ...args) => {
  if (isDev) {
    console.warn(message, ...args);
  } else {
    console.warn(message);
  }
};

// 通过 contextBridge 安全暴露 API
contextBridge.exposeInMainWorld('electronAPI', {
  // 获取设备列表
  getDeviceList: () => ipcRenderer.invoke('get-device-list'),

  // 发送控制指令
  sendCommand: (sn, commandData) => ipcRenderer.invoke('send-command', { sn, command: commandData }),

  // 新增设置相关 API
  saveBasicSettings: (settings) => ipcRenderer.invoke('save-basic-settings', settings),
  saveAdvancedSettings: (settings) => ipcRenderer.invoke('save-advanced-settings', settings),

  // 添加新的API
  selectFiles: (options) => ipcRenderer.invoke('select-files', options),
  getSolutions: () => ipcRenderer.invoke('get-solutions'),
  createSolution: (data) => ipcRenderer.invoke('create-solution', data),
  updateSolution: (data) => ipcRenderer.invoke('update-solution', data),
  deploySolution: (data) => ipcRenderer.invoke('deploy-solution', data),
  exportSolution: (data) => ipcRenderer.invoke('export-solution', data),
  cancelDeploy: (deviceSN) => ipcRenderer.invoke('cancel-deploy', deviceSN),
  onTransferProgress: (callback) => {
    const handler = (_, data) => {
      if (isDev) {
        console.log('preload 收到进度事件:', data);
      }
      callback(data);
    };
    ipcRenderer.on('transfer-progress', handler);
    return handler; // 返回处理函数，以便后续移除
  },
  offTransferProgress: (handler) => {
    if (!handler) {
      if (isDev) {
        console.warn('offTransferProgress: handler is undefined');
      }
      return;
    }
    if (isDev) {
      console.log('移除进度事件监听器');
    }
    ipcRenderer.removeListener('transfer-progress', handler);
  },
  onDeployError: (callback) => {
    const handler = (_, data) => {
      if (isDev) {
        console.log('preload 收到错误事件:', data);
      }
      callback(data);
    };
    ipcRenderer.on('deploy-error', handler);
    return handler; // 返回处理函数，以便后续移除
  },
  offDeployError: (handler) => {
    if (!handler) {
      if (isDev) {
        console.warn('offDeployError: handler is undefined');
      }
      return;
    }
    if (isDev) {
      console.log('移除错误事件监听器');
    }
    ipcRenderer.removeListener('deploy-error', handler);
  },
  getSettings: () => ipcRenderer.invoke('get-settings'),
  saveSettings: (settings) => ipcRenderer.invoke('save-settings', settings),

  // 添加资源相关的 API
  createResource: (data) => ipcRenderer.invoke('create-resource', data),
  getResources: () => ipcRenderer.invoke('get-resources'),
  deleteResource: (uuid) => ipcRenderer.invoke('delete-resource', uuid),
  deleteSolution: (uuid) => ipcRenderer.invoke('delete-solution', uuid),
  hasConfigFile: () => ipcRenderer.invoke('has-config-file'),
  openResourcesFolder: () => ipcRenderer.invoke('open-resources-folder'),
  getResourceFilePath: (relativePath) => ipcRenderer.invoke('get-resource-file-path', relativePath),
  updateResource: (resource) => ipcRenderer.invoke('update-resource', resource),
  getFileStats: (filePath) => ipcRenderer.invoke('get-file-stats', filePath),

  // 添加窗口控制API
  minimizeWindow: () => ipcRenderer.invoke('window-control', 'minimize'),
  maximizeWindow: () => ipcRenderer.invoke('window-control', 'maximize'),
  closeWindow: () => ipcRenderer.invoke('window-control', 'close'),
  windowControl: (command) => ipcRenderer.invoke('window-control', command),

  // 添加删除Logo的API
  deleteSolutionLogo: () => ipcRenderer.invoke('delete-solution-logo'),

  // 添加保存Logo的API
  saveSolutionLogo: (filePath) => ipcRenderer.invoke('save-solution-logo', filePath),

  // 添加保存背景图的API
  saveSolutionBackground: (filePath) => ipcRenderer.invoke('save-solution-background', filePath),

  // 添加删除背景图的API
  deleteSolutionBackground: () => ipcRenderer.invoke('delete-solution-background'),

  // 添加检查config文件夹大小的API
  checkConfigFolderSize: () => ipcRenderer.invoke('solution:checkConfigSize'),

  // 添加应用 Logo 相关的 API
  saveAppLogo: (filePath) => ipcRenderer.invoke('save-app-logo', filePath),
  getAppLogo: () => ipcRenderer.invoke('get-app-logo'),
  deleteAppLogo: () => ipcRenderer.invoke('delete-app-logo'),

  // 添加保存配置的 API
  saveConfig: (config) => ipcRenderer.invoke('save-config', config),
  saveConfigByType: (config, configType) => ipcRenderer.invoke('save-config-by-type', { config, configType }),
  getConfig: (configType) => ipcRenderer.invoke('get-config', configType),
  // 已废弃，请使用 deleteSolutionGroup
  deleteGroup: (groupName) => {
    console.warn('deleteGroup 方法已废弃，请使用 deleteSolutionGroup(solutionId, name)');
    return ipcRenderer.invoke('delete-group', groupName);
  },

  // 设备相关 API
  getConnectedDevices: () => ipcRenderer.invoke('get-connected-devices'),
  getDeviceHistory: () => ipcRenderer.invoke('get-device-history'),
  isDeviceOnline: (sn) => ipcRenderer.invoke('is-device-online', sn),
  deleteDevice: (sn) => ipcRenderer.invoke('delete-device', sn),
  addDevice: (sn) => ipcRenderer.invoke('add-device', sn),
  getAllDevices: () => ipcRenderer.invoke('get-all-devices'),
  isDeviceAdded: (sn) => ipcRenderer.invoke('is-device-added', sn),
  updateDeviceId: (sn, id, forceSwap = false) => ipcRenderer.invoke('update-device-id', { sn, id, forceSwap }),

  // 设备音量控制 API
  getDeviceVolume: (sn) => ipcRenderer.invoke('get-device-volume', sn),
  setDeviceVolume: (sn, volume) => ipcRenderer.invoke('set-device-volume', { sn, volume }),

  // 设备屏幕控制 API
  turnScreenOn: (sn) => ipcRenderer.invoke('turn-screen-on', sn),
  turnScreenOff: (sn) => ipcRenderer.invoke('turn-screen-off', sn),

  // 显示隐藏快捷入口
  quickAccess: (sn, isShow = true) => ipcRenderer.invoke('quick-access', { sn, isShow }),

  // 设备定位控制 API
  startLocateDevice: (sn) => ipcRenderer.invoke('start-locate-device', sn),
  stopLocateDevice: (sn) => ipcRenderer.invoke('stop-locate-device', sn),

  // 设备视野控制 API
  resetDeviceView: (sn) => ipcRenderer.invoke('reset-device-view', sn),

  // 应用控制 API
  controlApp: (sn, packageName, open, resourceIndex) => ipcRenderer.invoke('control-app', sn, packageName, open, resourceIndex),

  // 设备事件监听
  onDeviceConnected: (callback) => {
    const handler = (_, data) => callback(data);
    ipcRenderer.on('device-connected', handler);
    return handler; // 返回处理函数，以便后续移除
  },
  offDeviceConnected: (handler) => {
    ipcRenderer.removeListener('device-connected', handler);
  },
  onDeviceDisconnected: (callback) => {
    const handler = (_, ip) => callback(ip);
    ipcRenderer.on('device-disconnected', handler);
    return handler; // 返回处理函数，以便后续移除
  },
  offDeviceDisconnected: (handler) => {
    ipcRenderer.removeListener('device-disconnected', handler);
  },
  onDeviceStatusUpdate: (callback) => {
    const handler = (_, data) => callback(data);
    ipcRenderer.on('device-status-update', handler);
    return handler; // 返回处理函数，以便后续移除
  },
  offDeviceStatusUpdate: (handler) => {
    ipcRenderer.removeListener('device-status-update', handler);
  },
  onDeviceAdded: (callback) => {
    const handler = (_, data) => callback(data);
    ipcRenderer.on('device-added', handler);
    return handler; // 返回处理函数，以便后续移除
  },
  offDeviceAdded: (handler) => {
    ipcRenderer.removeListener('device-added', handler);
  },
  onScreenCastResponse: (callback) => {
    const handler = (_, data) => callback(data);
    ipcRenderer.on('screen-cast-response', handler);
    return handler; // 返回处理函数，以便后续移除
  },
  removeScreenCastResponseListener: (handler) => {
    ipcRenderer.removeListener('screen-cast-response', handler);
  },
  onScreenCastStopResponse: (callback) => {
    const handler = (_, data) => callback(data);
    ipcRenderer.on('screen-cast-stop-response', handler);
    return handler; // 返回处理函数，以便后续移除
  },
  removeScreenCastStopResponseListener: (handler) => {
    ipcRenderer.removeListener('screen-cast-stop-response', handler);
  },

  // 音量响应事件监听
  onVolumeResponse: (callback) => {
    const handler = (_, data) => callback(data);
    ipcRenderer.on('volume-response', handler);
    return handler; // 返回处理函数，以便后续移除
  },
  removeVolumeResponseListener: (handler) => {
    ipcRenderer.removeListener('volume-response', handler);
  },

  // 设置音量响应事件监听
  onSetVolumeResponse: (callback) => {
    const handler = (_, data) => callback(data);
    ipcRenderer.on('set-volume-response', handler);
    return handler; // 返回处理函数，以便后续移除
  },
  removeSetVolumeResponseListener: (handler) => {
    ipcRenderer.removeListener('set-volume-response', handler);
  },

  // 点亮屏幕响应事件监听
  onScreenOnResponse: (callback) => {
    const handler = (_, data) => callback(data);
    ipcRenderer.on('screen-on-response', handler);
    return handler; // 返回处理函数，以便后续移除
  },
  removeScreenOnResponseListener: (handler) => {
    ipcRenderer.removeListener('screen-on-response', handler);
  },

  // 关闭屏幕响应事件监听
  onScreenOffResponse: (callback) => {
    const handler = (_, data) => callback(data);
    ipcRenderer.on('screen-off-response', handler);
    return handler; // 返回处理函数，以便后续移除
  },
  removeScreenOffResponseListener: (handler) => {
    ipcRenderer.removeListener('screen-off-response', handler);
  },

  // 开启定位响应事件监听
  onLocateStartResponse: (callback) => {
    const handler = (_, data) => callback(data);
    ipcRenderer.on('locate-start-response', handler);
    return handler; // 返回处理函数，以便后续移除
  },
  removeLocateStartResponseListener: (handler) => {
    ipcRenderer.removeListener('locate-start-response', handler);
  },

  // 停止定位响应事件监听
  onLocateStopResponse: (callback) => {
    const handler = (_, data) => callback(data);
    ipcRenderer.on('locate-stop-response', handler);
    return handler; // 返回处理函数，以便后续移除
  },
  removeLocateStopResponseListener: (handler) => {
    ipcRenderer.removeListener('locate-stop-response', handler);
  },

  // 重置视野响应事件监听
  onResetViewResponse: (callback) => {
    const handler = (_, data) => callback(data);
    ipcRenderer.on('reset-view-response', handler);
    return handler; // 返回处理函数，以便后续移除
  },
  removeResetViewResponseListener: (handler) => {
    ipcRenderer.removeListener('reset-view-response', handler);
  },

  // 应用播控响应事件监听
  onAppControlResponse: (callback) => {
    const handler = (_, data) => callback(data);
    ipcRenderer.on('app-control-response', handler);
    return handler; // 返回处理函数，以便后续移除
  },
  removeAppControlResponseListener: (handler) => {
    ipcRenderer.removeListener('app-control-response', handler);
  },

  // 添加发布资源的 API
  publishResource: (resourceIndex, deviceList, loopPlay) =>
    ipcRenderer.invoke('publish-resource', { resourceIndex, deviceList, loopPlay }),

  // 停止播放
  stopResource: (deviceList, resourceType, packageName) => {
    console.log('[preload] 停止播放', deviceList, resourceType, packageName);
    return ipcRenderer.invoke('stop-resource', { deviceList, resourceType, packageName });
  },

  // 方案分组相关 API
  addSolutionGroup: (solutionId, name, description) =>
    ipcRenderer.invoke('add-solution-group', { solutionId, name, description }),
  updateSolutionGroup: (solutionId, oldName, newName, description) =>
    ipcRenderer.invoke('update-solution-group', { solutionId, oldName, newName, description }),
  deleteSolutionGroup: (params) => {
    // 检查参数是否是对象
    if (typeof params === 'object' && params !== null) {
      // 使用对象参数
      const { solutionId, name, deleteResources } = params;
      console.log(`[preload] 接收到对象参数: solutionId=${solutionId}, name=${name}, deleteResources=${deleteResources} (${typeof deleteResources})`);

      // 直接使用原始参数，不做任何转换
      return ipcRenderer.invoke('delete-solution-group', {
        solutionId,
        name,
        deleteResources
      });
    } else {
      // 兼容旧的位置参数调用方式
      const solutionId = arguments[0];
      const name = arguments[1];
      const deleteResources = arguments[2] === true;

      console.log(`[preload] 接收到位置参数: solutionId=${solutionId}, name=${name}, deleteResources=${deleteResources} (${typeof deleteResources})`);

      return ipcRenderer.invoke('delete-solution-group', {
        solutionId,
        name,
        deleteResources
      });
    }
  },

  // 设备组相关 API
  getDeviceGroups: () => ipcRenderer.invoke('get-device-groups'),
  getDeviceGroup: (groupId) => ipcRenderer.invoke('get-device-group', groupId),
  createDeviceGroup: (name, description) => ipcRenderer.invoke('create-device-group', { name, description }),
  updateDeviceGroup: (groupId, data) => ipcRenderer.invoke('update-device-group', { groupId, data }),
  deleteDeviceGroup: (groupId) => ipcRenderer.invoke('delete-device-group', groupId),
  addDeviceToGroup: (groupId, sn) => ipcRenderer.invoke('add-device-to-group', { groupId, sn }),
  removeDeviceFromGroup: (groupId, sn) => ipcRenderer.invoke('remove-device-from-group', { groupId, sn }),
  getDevicesByGroup: (groupId) => ipcRenderer.invoke('get-devices-by-group', groupId),

  // 事件发送 API
  emit: (channel, ...args) => ipcRenderer.send(channel, ...args),

  // 事件监听 API
  on: (channel, callback) => {
    const handler = (_, ...args) => callback(...args);
    ipcRenderer.on(channel, handler);
    return handler;
  },
  off: (channel, handler) => {
    ipcRenderer.removeListener(channel, handler);
  },

  // RTSP流代理服务 API
  startRtspStream: (sn, rtspUrl) => ipcRenderer.invoke('start-rtsp-stream', { sn, rtspUrl }),
  stopRtspStream: (sn) => ipcRenderer.invoke('stop-rtsp-stream', sn),
  getStreamInfo: (sn) => ipcRenderer.invoke('get-stream-info', sn),
  getAllStreams: () => ipcRenderer.invoke('get-all-streams'),

  // 应用信息 API
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  getVersion: async () => {
    const versionInfo = await ipcRenderer.invoke('get-app-version');
    return versionInfo.version;
  },
  getPlatformInfo: () => ipcRenderer.invoke('get-platform-info'),

  // 升级相关 API
  checkUpdate: () => ipcRenderer.invoke('check-update'),
  downloadUpdate: () => ipcRenderer.invoke('download-update'),
  installUpdate: () => ipcRenderer.invoke('install-update'),
  onUpdateProgress: (callback) => {
    const handler = (_, data) => callback(data);
    ipcRenderer.on('update-progress', handler);
    return handler;
  },
  offUpdateProgress: (handler) => {
    ipcRenderer.removeListener('update-progress', handler);
  },

  // 发布记录相关 API
  getPublishRecords: () => ipcRenderer.invoke('get-publish-records'),
  createPublishRecord: (recordData) => ipcRenderer.invoke('create-publish-record', recordData),
  updatePublishRecord: (id, updateData) => ipcRenderer.invoke('update-publish-record', id, updateData),
  stopPublishRecord: (id) => ipcRenderer.invoke('stop-publish-record', id),
  activatePublishRecord: (id) => ipcRenderer.invoke('activate-publish-record', id),
  deletePublishRecord: (id) => ipcRenderer.invoke('delete-publish-record', id),
  clearPublishRecords: () => ipcRenderer.invoke('clear-publish-records'),

  // 批量控制音量响应事件监听
  onBatchVolumeResponse: (callback) => {
    const handler = (_, data) => callback(data);
    ipcRenderer.on('device:batch-volume-result', handler);
    return handler;
  },
  removeBatchVolumeResponseListener: (handler) => {
    ipcRenderer.removeListener('device:batch-volume-result', handler);
  },

  // 批量控制屏幕响应事件监听
  onBatchScreenResponse: (callback) => {
    const handler = (_, data) => callback(data);
    ipcRenderer.on('device:batch-screen-result', handler);
    return handler;
  },
  removeBatchScreenResponseListener: (handler) => {
    ipcRenderer.removeListener('device:batch-screen-result', handler);
  },

  // 批量控制快捷入口响应事件监听
  onBatchShortcutResponse: (callback) => {
    const handler = (_, data) => callback(data);
    ipcRenderer.on('device:batch-shortcut-result', handler);
    return handler;
  },
  removeBatchShortcutResponseListener: (handler) => {
    ipcRenderer.removeListener('device:batch-shortcut-result', handler);
  },

  // 快捷入口响应事件监听
  onQuickAccessResponse: (callback) => {
    const handler = (_, data) => callback(data);
    ipcRenderer.on('quick-access-response', handler);
    return handler;
  },
  removeQuickAccessResponseListener: (handler) => {
    ipcRenderer.removeListener('quick-access-response', handler);
  },
});
